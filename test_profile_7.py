#!/usr/bin/env python3
"""
Test script specifically for profile 7 with captured ElevenLabs data
"""

import asyncio
import aiohttp
import json

async def test_profile_7():
    """Test profile 7 with captured data"""
    try:
        # Login first
        async with aiohttp.ClientSession() as session:
            login_data = {
                "email": "<EMAIL>",
                "password": "password123"
            }
            
            print("🔐 Logging in...")
            async with session.post("http://localhost:3000/auth/login", json=login_data) as response:
                if response.status in [200, 201]:
                    result = await response.json()
                    access_token = result.get('access_token')
                    print(f"✅ Login successful")
                else:
                    print(f"❌ Login failed: {response.status}")
                    return False
        
        # Test profile 7 launch
        headers = {"Authorization": f"Bearer {access_token}"}
        async with aiohttp.ClientSession(headers=headers) as session:
            print("\n🚀 Testing profile 7 launch...")
            
            async with session.post("http://localhost:3000/profiles/7/launch-browser-with-data") as response:
                if response.status in [200, 201]:
                    result = await response.json()
                    print(f"✅ Profile 7 browser launched successfully!")
                    print(f"   - Browser type: {result.get('data', {}).get('browser_type')}")
                    print(f"   - View mode: {result.get('data', {}).get('view_mode')}")
                    print(f"   - Profile data loaded: {result.get('data', {}).get('profile_data_loaded')}")
                    print(f"   - Data source: {result.get('data', {}).get('data_source')}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Profile 7 launch failed: {response.status} - {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

async def main():
    print("🔍 Testing Profile 7 with ElevenLabs captured data...")
    
    success = await test_profile_7()
    
    if success:
        print("\n🎉 Profile 7 test successful!")
        print("\n📋 Expected results:")
        print("   - Browser should open with ElevenLabs website")
        print("   - Should be logged in to ElevenLabs account")
        print("   - localStorage should contain auth tokens")
        print("   - Cookies should be properly injected")
        print("   - Should navigate to https://elevenlabs.io/app/home")
        print("\n⚠️ Check browser window to verify login state!")
    else:
        print("\n❌ Profile 7 test failed")

if __name__ == "__main__":
    asyncio.run(main())
