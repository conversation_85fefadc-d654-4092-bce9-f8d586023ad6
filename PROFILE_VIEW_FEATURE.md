# Profile View Feature - Implementation Guide

## 🎯 Overview

This document describes the implementation of the **Profile View Feature** that allows users to view and launch browsers with saved profile data from compressed files in the `auto-login/compressed/` directory.

## 🚀 Features Implemented

### 1. **View Profile Data**
- Load profile data from compressed gzip files
- Display metadata including compression statistics
- Fallback to database data if compressed files not available
- Show data source (compressed file vs database)

### 2. **Launch Browser with Profile Data**
- Launch browser with pre-loaded profile data
- Restore localStorage, cookies, history, and indexedDB
- Maintain login state without re-authentication
- Support for both compressed and database data sources

### 3. **Enhanced UI Components**
- New "View Profile" button in ProfileList
- Enhanced ProfileDataModal with compression info
- "Launch with Profile Data" button in modal
- Visual indicators for data source and compression stats

## 📁 File Structure

```
auto-login/
├── compressed/                          # Compressed profile data
│   ├── profile_Profile_*_latest.json.gz # Compressed profile data
│   └── profile_Profile_*_latest.meta.json # Metadata files
├── scripts/
│   ├── create-admin-user.js            # Create admin user and test accounts
│   ├── create-sample-profile-data.js   # Generate sample compressed data
│   ├── test-profile-workflow.js        # Test the complete workflow
│   └── setup-profile-view-feature.sh   # Complete setup script
└── src/modules/profiles/
    ├── profiles.controller.ts           # New API endpoints
    └── profiles.service.ts              # Profile data loading logic

frontend/src/
├── components/ProfileManagement/
│   ├── ProfileList.js                  # Enhanced with View Profile button
│   └── ProfileDataModal.js             # Enhanced with compression info
└── services/
    └── api.js                          # New API methods
```

## 🔧 API Endpoints

### New Endpoints Added:

1. **GET `/profiles/:accountId/profile-data`**
   - Load profile data from compressed files or database
   - Returns metadata and compression statistics
   - Includes data source information

2. **POST `/profiles/:accountId/launch-browser-with-data`**
   - Launch browser with pre-loaded profile data
   - Calls Python backend with profile data
   - Logs access for audit purposes

## 🛠️ Setup Instructions

### Quick Setup (Recommended)

```bash
cd auto-login
./scripts/setup-profile-view-feature.sh
```

### Manual Setup

1. **Create Admin User and Test Accounts:**
```bash
cd auto-login
node scripts/create-admin-user.js
```

2. **Generate Sample Profile Data:**
```bash
node scripts/create-sample-profile-data.js
```

3. **Test the Workflow:**
```bash
node scripts/test-profile-workflow.js
```

## 🧪 Testing

### Admin Credentials
- **Email:** `<EMAIL>`
- **Password:** `password123`

### Test Accounts Created
- Account ID 1: `<EMAIL>`
- Account ID 2: `<EMAIL>`

### Testing Steps

#### Option 1: Complete Test Script (Recommended)
```bash
./scripts/test-complete-profile-view.sh
```
*Automatically starts all services: Python Backend, NestJS Backend, and React Frontend*

#### Option 2: Manual Testing
```bash
# Terminal 1: Start Python Backend
./scripts/start-python-backend.sh

# Terminal 2: Start NestJS Backend
cd auto-login
npm run start:dev

# Terminal 3: Start Frontend
cd frontend
npm start
```

#### Test the Features:
1. **Login and Navigate:**
   - Open http://localhost:3001
   - Login with admin credentials
   - Navigate to "Manage Profiles" page

2. **Test Profile View Features:**
   - Click **👁️ "View Profile"** button to launch Camoufox browser with saved data
   - Click **📊 "View Profile Data"** button to see compression details
   - In the modal, click **🚀 "Launch with Profile Data"** for alternative launch method

## 📊 Data Format

### Compressed Profile Data Structure:
```json
{
  "localStorage": {
    "facebook_user_id": "123456789",
    "facebook_session": "active_session_token"
  },
  "indexedDB": {
    "facebook_cache": {
      "friends_list": [...],
      "messages": [...]
    }
  },
  "history": [
    {
      "url": "https://facebook.com",
      "title": "Facebook",
      "visitTime": 1640995200000
    }
  ],
  "cookies": [
    {
      "name": "c_user",
      "value": "123456789",
      "domain": ".facebook.com",
      "path": "/",
      "expires": 1640995200000,
      "httpOnly": false,
      "secure": true
    }
  ],
  "current_page": {
    "url": "https://facebook.com/home",
    "title": "Facebook - Home",
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "metadata": {
    "browser": "Camoufox",
    "version": "1.0.0",
    "os": "Windows 10",
    "saved_by": "admin",
    "saved_at": "2024-01-01T00:00:00.000Z"
  }
}
```

### Metadata File Structure:
```json
{
  "profile_id": "Profile_1_1640995200000_abc123",
  "created_at": "2024-01-01T00:00:00.000Z",
  "original_size": 45658,
  "compressed_size": 6982,
  "compression_ratio": 0.1529,
  "checksum": "f2e444b4ae70fd3ef7e1d693b95f8ccb...",
  "data_types": ["localStorage", "indexedDB", "history", "cookies"],
  "version": "2.0",
  "format": "gzip_json"
}
```

## 🔍 Key Implementation Details

### 1. **Profile Data Loading Logic**
```typescript
// profiles.service.ts
async getProfileData(accountId: number) {
  // 1. Try to load from compressed files first
  const compressedData = await this.loadCompressedProfileData(accountId);
  if (compressedData) {
    return {
      accountId,
      profileData: compressedData.profileData,
      metadata: compressedData.metadata,
      source: 'compressed_file'
    };
  }
  
  // 2. Fallback to database data
  // ... database loading logic
}
```

### 2. **File Naming Convention**
- Compressed files: `profile_Profile_{accountId}_{timestamp}_{randomId}_latest.json.gz`
- Metadata files: `profile_Profile_{accountId}_{timestamp}_{randomId}_latest.meta.json`

### 3. **Browser Launch Integration**
- Calls Python backend at `http://localhost:8000/api/profiles/launch-with-data`
- Passes complete profile data for restoration
- Handles both compressed and database data sources

## 🎨 UI Enhancements

### ProfileList Component
- **New Button:** "View Profile" (👁️) - Launch browser with saved data
- **Enhanced Tooltip:** Clear description of functionality
- **Visual Styling:** Blue color to distinguish from other actions

### ProfileDataModal Component
- **Data Source Indicator:** Shows whether data comes from compressed file or database
- **Compression Statistics:** Original size, compressed size, compression ratio
- **Data Types:** Visual tags showing available data types
- **Launch Button:** "Launch with Profile Data" for direct browser launch

## 🔒 Security & Access Control

- **Admin Access:** Full access to all profiles
- **User Access:** Requires explicit permissions via profile sharing
- **Audit Logging:** All profile access is logged with timestamps
- **Data Validation:** Checksum verification for compressed files

## 🚨 Error Handling

- **File Not Found:** Graceful fallback to database data
- **Decompression Errors:** Error logging with fallback
- **Backend Unavailable:** Clear error messages for users
- **Access Denied:** Proper permission checking and messaging

## 📈 Performance Considerations

- **Compression:** Significant size reduction (typically 80-90%)
- **Caching:** Metadata loaded separately for quick access
- **Lazy Loading:** Profile data loaded only when needed
- **Timeout Handling:** 60-second timeout for browser launch operations

## 🔄 Future Enhancements

1. **Profile Data Versioning:** Support for multiple versions of saved data
2. **Selective Data Loading:** Choose which data types to restore
3. **Profile Comparison:** Compare different versions of profile data
4. **Bulk Operations:** Launch multiple profiles simultaneously
5. **Data Export:** Export profile data in various formats

## 🐛 Troubleshooting

### Common Issues:

1. **"No compressed profile data found"**
   - Run `node scripts/create-sample-profile-data.js`
   - Check if `compressed/` directory exists

2. **"Python backend is not available"**
   - Ensure Python backend is running on port 8000
   - Check backend logs for errors

3. **"Access denied to this profile"**
   - Login as admin or request profile access
   - Check user permissions in database

4. **Database connection errors**
   - Verify PostgreSQL is running
   - Check database credentials in `.env`

## 📞 Support

For issues or questions about this feature:
1. Check the troubleshooting section above
2. Review the test scripts for examples
3. Check backend logs for detailed error messages
4. Verify all setup steps were completed successfully

## 📊 Expected Results

### When clicking **"View Profile"** button:
```
✅ Browser launched with saved profile data: Profile_X
✅ Camoufox browser opens with pre-loaded cookies and localStorage
✅ Facebook login state is preserved (no need to login again)
```

### When clicking **"View Profile Data"** button:
- Modal displays compression statistics
- Data source: "Compressed File"
- Original size, compressed size, compression ratio
- Data types available (localStorage, cookies, history, etc.)
- **🚀 "Launch with Profile Data"** button works in modal

## 🎯 Key Features Working

### ✅ **Real Browser Launch**
- Camoufox browser actually opens (not demo mode)
- Full antidetect features enabled
- Profile data injected into browser context

### ✅ **Profile Data Injection**
- localStorage restored from compressed files
- Cookies loaded and applied
- Session state preserved
- No need to login again

### ✅ **Complete Integration**
- NestJS ↔ Python Backend communication
- Compressed file reading with gzip decompression
- Error handling and fallback mechanisms

## 🔧 Issue Fixed: Real vs Mock Data

### ❌ **Previous Issue**
- System was loading **mock test data** instead of real captured browser data
- Mock data contained fake values like:
  - Facebook user ID: `777888999`
  - Session token: `active_session_token_007`
  - Friends: Emma Watson, Ryan Gosling
  - Generic Facebook URLs

### ✅ **Solution Applied**
- **Fixed file priority logic** to prefer real captured data over mock data
- **Removed mock data files** that were interfering with real data loading
- **Updated file search pattern** to prioritize `profile_Profile_*` (real data) over `Profile_*` (mock data)

### 🎯 **Current Status**
- ✅ **Real captured data loading**: ElevenLabs website data with PostHog analytics
- ✅ **Actual cookies and localStorage**: Real session data from captured browser
- ✅ **Proper URLs**: Real website URLs instead of generic Facebook links
- ✅ **Authentic session state**: Actual user session preserved

---

**✅ Feature Status:** Fully implemented and tested with REAL DATA
**🎯 Ready for Production:** Yes, with proper backend setup
**🚀 Real Browser Launch:** Camoufox browser opens with authentic captured profile data
**📊 Data Integrity:** Real captured browser data (not mock/test data)
