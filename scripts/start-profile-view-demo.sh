#!/bin/bash

echo "🚀 Starting Profile View Feature Demo"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -d "auto-login" ] || [ ! -d "frontend" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_info "Starting services for Profile View Feature demo..."

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 0
    else
        return 1
    fi
}

# Check if NestJS backend is running
if check_port 3000; then
    print_status "NestJS backend is already running on port 3000"
else
    print_info "Starting NestJS backend..."
    cd auto-login
    npm run start:dev &
    NESTJS_PID=$!
    cd ..
    print_status "NestJS backend starting... (PID: $NESTJS_PID)"
fi

# Wait a moment for backend to start
sleep 3

# Check if frontend is running
if check_port 3001; then
    print_status "Frontend is already running on port 3001"
else
    print_info "Starting React frontend..."
    cd frontend
    npm start &
    FRONTEND_PID=$!
    cd ..
    print_status "React frontend starting... (PID: $FRONTEND_PID)"
fi

# Wait for services to start
print_info "Waiting for services to start..."
sleep 5

echo -e "\n🎯 Demo Instructions"
echo "===================="

print_status "Services should now be running:"
echo "   • NestJS Backend: http://localhost:3000"
echo "   • React Frontend: http://localhost:3001"

echo -e "\n📋 Demo Steps:"
echo "1. Open your browser and go to: http://localhost:3001"
echo ""
echo "2. Login with admin credentials:"
echo "   📧 Email: <EMAIL>"
echo "   🔑 Password: password123"
echo ""
echo "3. Navigate to 'Manage Profiles' page"
echo ""
echo "4. Test the new Profile View features:"
echo "   👁️  Click 'View Profile' button (blue eye icon)"
echo "      → This launches browser with saved profile data"
echo ""
echo "   📊 Click 'View Profile Data' button (database icon)"
echo "      → This opens modal showing compressed data details"
echo ""
echo "   🚀 In the modal, click 'Launch with Profile Data'"
echo "      → Alternative way to launch browser with data"

echo -e "\n📊 Sample Data Available:"
echo "   • Account ID 1: <EMAIL>"
echo "   • Account ID 2: <EMAIL>"
echo "   • Both have compressed profile data with:"
echo "     - localStorage data"
echo "     - Cookies"
echo "     - Browser history"
echo "     - IndexedDB data"

echo -e "\n🔍 What to Look For:"
echo "   ✓ Data source indicator (Compressed File vs Database)"
echo "   ✓ Compression statistics (original vs compressed size)"
echo "   ✓ Data types available (localStorage, cookies, etc.)"
echo "   ✓ Successful browser launch messages"

echo -e "\n⚠️  Notes:"
echo "   • Browser launch will show success message but won't actually open browser"
echo "     (Python backend integration required for actual browser launch)"
echo "   • Profile data loading and display should work perfectly"
echo "   • All UI enhancements should be visible"

echo -e "\n🛑 To Stop Services:"
echo "   Press Ctrl+C in this terminal, or run:"
echo "   pkill -f 'npm run start:dev'"
echo "   pkill -f 'npm start'"

echo -e "\n🎉 Demo is ready! Open http://localhost:3001 and <NAME_EMAIL> / password123"

# Keep script running to show logs
print_info "Monitoring services... Press Ctrl+C to stop"

# Function to cleanup on exit
cleanup() {
    echo -e "\n🛑 Stopping services..."
    if [ ! -z "$NESTJS_PID" ]; then
        kill $NESTJS_PID 2>/dev/null
        print_status "NestJS backend stopped"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        print_status "Frontend stopped"
    fi
    print_status "Demo stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait indefinitely
while true; do
    sleep 1
done
