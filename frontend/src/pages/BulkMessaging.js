/**
 * Bulk Messaging Page Component - Profile-based messaging
 */

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
  Card, Form, Input, Select, Button, Space, Upload, Table, Progress,
  message, Row, Col, InputNumber, Switch, Tag, Modal, Divider, Tooltip,
  Alert, List, Statistic, Steps, Typography
} from 'antd';
import {
  MessageOutlined, UploadOutlined, PlayCircleOutlined, StopOutlined,
  EyeOutlined, DownloadOutlined, DeleteOutlined, ReloadOutlined,
  UserOutlined, CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined,
  FileExcelOutlined, SendOutlined, UpOutlined, DownOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;
const { Step } = Steps;

const BulkMessaging = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [profiles, setProfiles] = useState([]);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [fileValidation, setFileValidation] = useState(null);
  const [activeTask, setActiveTask] = useState(null);
  const [taskProgress, setTaskProgress] = useState({});
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [selectedTaskResults, setSelectedTaskResults] = useState(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedTasks, setCompletedTasks] = useState([]);
  const [showCompletedTasks, setShowCompletedTasks] = useState(false);

  // Add flags to prevent multiple simultaneous API calls
  const [isInitialDataLoaded, setIsInitialDataLoaded] = useState(false);
  const [isPolling, setIsPolling] = useState(false);

  // Add ref to track component mount state and intervals
  const isMountedRef = useRef(true);
  const intervalRef = useRef(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('🧹 [BulkMessaging] Component unmounting, cleaning up...');
      isMountedRef.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);

  console.log('🔄 [BulkMessaging] Component render - loading:', loading, 'isInitialDataLoaded:', isInitialDataLoaded, 'activeTask:', activeTask, 'isPolling:', isPolling, 'profiles:', profiles.length, 'completedTasks:', completedTasks.length);

  // Helper function to retry API calls with exponential backoff
  const retryApiCall = async (apiCall, maxRetries = 3, baseDelay = 1000) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        if (error.response?.status === 429 && attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt - 1);
          console.log(`🔄 Rate limit hit, retrying in ${delay}ms (attempt ${attempt}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        throw error;
      }
    }
  };

  // Debounce mechanism to prevent rapid API calls
  const lastApiCallTime = React.useRef(0);
  const minApiCallInterval = 1000; // Minimum 1 second between API calls

  const debounceApiCall = async (apiCall, callName = 'API') => {
    const now = Date.now();
    const timeSinceLastCall = now - lastApiCallTime.current;

    if (timeSinceLastCall < minApiCallInterval) {
      const waitTime = minApiCallInterval - timeSinceLastCall;
      console.log(`⏳ [BulkMessaging] Debouncing ${callName} call, waiting ${waitTime}ms`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    lastApiCallTime.current = Date.now();
    return await apiCall();
  };

  // Load initial data only once when component mounts
  useEffect(() => {
    console.log('🚀 [BulkMessaging] useEffect[initial] triggered - isInitialDataLoaded:', isInitialDataLoaded, 'loading:', loading);

    if (!isInitialDataLoaded && !loading) {
      console.log('🚀 [BulkMessaging] Component mounted, loading initial data...');
      loadInitialData();
    } else {
      console.log('⏭️ [BulkMessaging] Skipping initial data load - already loaded:', isInitialDataLoaded, 'or loading:', loading);
    }
  }, []); // Empty dependency array - only run once on mount

  // Set up polling for active tasks - separate useEffect
  useEffect(() => {
    console.log('🔄 [BulkMessaging] useEffect[polling] triggered - activeTask:', activeTask, 'isPolling:', isPolling);
    console.log('🔄 [BulkMessaging] taskProgress for activeTask:', activeTask ? taskProgress[activeTask] : 'no activeTask');

    if (!activeTask) {
      console.log('⏹️ [BulkMessaging] No active task, skipping polling setup');
      setIsPolling(false);
      return;
    }

    // Check if task is already completed
    if (taskProgress[activeTask] && ['completed', 'failed', 'cancelled'].includes(taskProgress[activeTask].status)) {
      console.log('⏹️ [BulkMessaging] Task already completed, skipping polling setup. Status:', taskProgress[activeTask].status);
      setIsPolling(false);
      return;
    }

    if (isPolling) {
      console.log('⏹️ [BulkMessaging] Already polling, skipping polling setup');
      return;
    }

    setIsPolling(true);
    console.log('🚀 [BulkMessaging] Starting polling for task:', activeTask);

    intervalRef.current = setInterval(() => {
      console.log('⏰ [BulkMessaging] Polling interval triggered for task:', activeTask);

      // Check if component is still mounted
      if (!isMountedRef.current) {
        console.log('🧹 [BulkMessaging] Component unmounted, stopping polling');
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
        return;
      }

      // Double check task status before polling
      if (activeTask && taskProgress[activeTask] &&
          ['completed', 'failed', 'cancelled'].includes(taskProgress[activeTask].status)) {
        console.log('⏹️ [BulkMessaging] Task completed during polling, stopping. Status:', taskProgress[activeTask].status);
        setIsPolling(false);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
        return;
      }

      if (activeTask) {
        console.log('📊 [BulkMessaging] Polling task status for:', activeTask);
        pollTaskStatus(activeTask);
      } else {
        console.log('⏹️ [BulkMessaging] No active task, stopping polling');
        setIsPolling(false);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      }
    }, 3000);

    console.log('✅ [BulkMessaging] Polling interval set up for task:', activeTask);

    return () => {
      console.log('🧹 [BulkMessaging] Cleaning up polling interval for task:', activeTask);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setIsPolling(false);
    };
  }, [activeTask]); // Only depend on activeTask to avoid infinite loops

  const loadInitialData = useCallback(async () => {
    const loadStartTime = Date.now();
    console.log('🚀 [BulkMessaging] loadInitialData() called at:', new Date().toISOString());
    console.log('📊 [BulkMessaging] Current state - loading:', loading, 'profiles:', profiles.length, 'activeTask:', activeTask);

    // Prevent multiple simultaneous calls
    if (loading || isInitialDataLoaded || !isMountedRef.current) {
      console.log('⚠️ [BulkMessaging] loadInitialData already in progress, completed, or component unmounted, skipping... loading:', loading, 'isInitialDataLoaded:', isInitialDataLoaded, 'isMounted:', isMountedRef.current);
      return;
    }

    try {
      if (!isMountedRef.current) return;
      setLoading(true);
      console.log('🔄 [BulkMessaging] Starting data load process...');

      // Load profiles with retry logic for rate limiting
      try {
        console.log('🔄 [BulkMessaging] Loading profiles...');
        const profilesData = await debounceApiCall(() => retryApiCall(() => apiService.getProfiles()), 'getProfiles');
        const filteredProfiles = profilesData.filter(p => p.facebook_logged_in);
        if (!isMountedRef.current) return;
        setProfiles(filteredProfiles);
        console.log('✅ [BulkMessaging] Profiles loaded successfully:', filteredProfiles.length, 'profiles');
      } catch (profileError) {
        console.log('❌ [BulkMessaging] Failed to load profiles:', profileError.message);

        // Handle rate limiting specifically
        if (profileError.response?.status === 429) {
          console.log('🔄 [BulkMessaging] Profiles failed due to rate limiting, trying to load completed tasks anyway...');
          message.warning('Rate limit reached while loading profiles. Please wait a moment and refresh.');
        } else {
          console.log('❌ [BulkMessaging] Failed to load data:', profileError);
          message.error('Failed to load profiles: ' + profileError.message);
        }
      }

      // Add a small delay to avoid hitting rate limits
      console.log('⏳ [BulkMessaging] Adding 500ms delay between API calls...');
      await new Promise(resolve => setTimeout(resolve, 500));

      // Load completed tasks from database
      console.log('🔄 [BulkMessaging] Loading completed tasks...');
      await loadCompletedTasks();

      const loadEndTime = Date.now();
      console.log('✅ [BulkMessaging] loadInitialData completed in:', (loadEndTime - loadStartTime), 'ms');
      if (!isMountedRef.current) return;
      setIsInitialDataLoaded(true);

    } catch (error) {
      console.log('❌ [BulkMessaging] Failed to load data:', error);
      if (isMountedRef.current) {
        message.error('Failed to load data: ' + error.message);
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
        console.log('🏁 [BulkMessaging] loadInitialData finished, loading set to false');
      }
    }
  }, [loading, isInitialDataLoaded, retryApiCall, debounceApiCall]); // Dependencies for useCallback

  const loadCompletedTasks = useCallback(async () => {
    const taskLoadStartTime = Date.now();
    console.log('📊 [BulkMessaging] loadCompletedTasks() called at:', new Date().toISOString());

    try {
      console.log('🔍 [API] GET /api/messaging/bulk/statistics?limit=20 - Headers:', {
        'Accept': 'application/json, text/plain, */*',
        'Authorization': `Bearer ${localStorage.getItem('token') || 'no-token'}`
      });
      console.log('🔍 [API] Request Headers:', {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json'
      });

      console.log('🌐 [BulkMessaging] Making API call to get statistics...');
      const response = await debounceApiCall(() => retryApiCall(() => apiService.get('/api/messaging/bulk/statistics?limit=20')), 'getStatistics');
      const completedTasksData = response.statistics || [];
      console.log('📈 [BulkMessaging] Received statistics data:', completedTasksData.length, 'tasks');

      if (!isMountedRef.current) return;

      // Convert to taskProgress format for UI compatibility
      const progressData = {};
      completedTasksData.forEach(stat => {
        if (stat.status === 'completed') {
          progressData[stat.task_id] = {
            task_id: stat.task_id,
            name: stat.task_name,
            status: stat.status,
            progress: 100,
            total_recipients: stat.total_recipients,
            messages_sent: stat.messages_sent,
            messages_failed: stat.messages_failed,
            messages_skipped: stat.messages_skipped,
            final_statistics: {
              total_recipients: stat.total_recipients,
              messages_sent: stat.messages_sent,
              messages_failed: stat.messages_failed,
              messages_skipped: stat.messages_skipped,
              success_rate: stat.success_rate,
              total_time_seconds: stat.total_time_seconds,
              total_time_formatted: stat.total_time_formatted,
              average_time_per_message: stat.average_time_per_message,
              completed_at: stat.completed_at,
              browser_closed: stat.browser_closed
            }
          };
        }
      });

      setCompletedTasks(completedTasksData);

      // If there's a recently completed task, show it
      if (completedTasksData.length > 0) {
        const latestCompleted = completedTasksData[0];
        if (latestCompleted.status === 'completed') {
          setTaskProgress(prev => ({
            ...prev,
            ...progressData
          }));

          // Show the latest completed task if no active task
          if (!activeTask) {
            console.log('📋 [BulkMessaging] Setting activeTask to latest completed task:', latestCompleted.task_id);
            // Set activeTask but don't start polling since it's already completed
            setActiveTask(latestCompleted.task_id);
            setIsPolling(false); // Ensure polling is disabled for completed tasks
          } else {
            console.log('⏭️ [BulkMessaging] Skipping activeTask update - already have active task:', activeTask);
          }
        }
      }

      const taskLoadEndTime = Date.now();
      console.log('✅ [BulkMessaging] Completed tasks loaded successfully in:', (taskLoadEndTime - taskLoadStartTime), 'ms');

    } catch (error) {
      const taskLoadEndTime = Date.now();
      console.error('❌ [BulkMessaging] Failed to load completed tasks in:', (taskLoadEndTime - taskLoadStartTime), 'ms', error);

      // Handle rate limiting specifically
      if (error.response?.status === 429) {
        console.log('🔄 [BulkMessaging] Statistics failed due to rate limiting');
        message.warning('Rate limit reached while loading task statistics. Please wait a moment and refresh.');
      } else {
        console.error('📊 Error response data:', error.response?.data || 'No response data');
        console.error('📊 Error response status:', error.response?.status || 'No status');
        // Don't show error message as this is not critical for basic functionality
      }
    }
  }, [activeTask, retryApiCall, debounceApiCall]); // Dependencies for useCallback

  const handleFileUpload = async (file) => {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('file', file);

      const result = await apiService.post('/api/messaging/bulk/validate-excel', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      if (result.valid) {
        setUploadedFile({
          file: file,
          file_path: result.file_path || file.name,
          total_recipients: result.total_recipients,
          url_type_breakdown: result.url_type_breakdown
        });
        setFileValidation(result);
        setCurrentStep(1);
        message.success(`Validated ${result.total_recipients} recipients successfully`);
      } else {
        message.error('File validation failed: ' + result.error);
        setFileValidation(result);
      }

      return false; // Prevent default upload behavior
    } catch (error) {
      message.error('Failed to validate file: ' + error.message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleStartBulkMessaging = async (values) => {
    try {
      setLoading(true);

      const config = {
        name: values.name,
        sender_profile_id: values.sender_profile_id,
        excel_file_path: uploadedFile?.file_path,
        message_content: values.message_content,
        message_type: values.message_type || 'text',
        delay_between_messages_min: values.delay_between_messages_min || 10,
        delay_between_messages_max: values.delay_between_messages_max || 30,
        scroll_distance_min: values.scroll_distance_min || 200,
        scroll_distance_max: values.scroll_distance_max || 800,
        reading_time_min: values.reading_time_min || 2.0,
        reading_time_max: values.reading_time_max || 5.0,
        stop_on_consecutive_failures: values.stop_on_consecutive_failures || 3,
        skip_sent_recipients: values.skip_sent_recipients !== false
      };

      const result = await apiService.post('/api/messaging/bulk/start', { config });

      setActiveTask(result.task_id);
      setCurrentStep(2);
      message.success('Bulk messaging task started successfully');

      // Reset form
      form.resetFields();
      setUploadedFile(null);
      setFileValidation(null);
      setCurrentStep(0);

    } catch (error) {
      message.error('Failed to start bulk messaging: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const pollTaskStatus = useCallback(async (taskId) => {
    const pollStartTime = Date.now();
    console.log('🔄 [BulkMessaging] pollTaskStatus() called for task:', taskId, 'at:', new Date().toISOString());

    try {
      console.log('🌐 [BulkMessaging] Making API call to get task status...');
      const status = await debounceApiCall(() => apiService.get(`/api/messaging/bulk/status/${taskId}`), 'pollTaskStatus');
      console.log('📊 [BulkMessaging] Received task status:', status.status, 'progress:', status.progress);

      setTaskProgress(prev => ({ ...prev, [taskId]: status }));

      // Stop polling if task is completed, but keep activeTask for UI display
      if (['completed', 'failed', 'cancelled'].includes(status.status)) {
        console.log('🏁 [BulkMessaging] Task completed with status:', status.status, '- stopping polling');
        setIsPolling(false); // Stop polling immediately
        // Don't set activeTask to null immediately - keep it for final statistics display
        // Only clear it when user manually clears results
        setCurrentStep(0);
      } else {
        console.log('⏳ [BulkMessaging] Task still running with status:', status.status, '- continuing polling');
      }

      const pollEndTime = Date.now();
      console.log('✅ [BulkMessaging] pollTaskStatus completed in:', (pollEndTime - pollStartTime), 'ms');

    } catch (error) {
      const pollEndTime = Date.now();
      console.error('❌ [BulkMessaging] Failed to poll task status in:', (pollEndTime - pollStartTime), 'ms', error);

      // If task not found (404), stop polling and clear activeTask
      if (error.response?.status === 404) {
        console.log('🚫 [BulkMessaging] Task not found (404), stopping polling and clearing activeTask');
        setIsPolling(false);
        setActiveTask(null);
        setCurrentStep(0);
      }
    }
  }, [debounceApiCall]); // Dependencies for useCallback

  const handleStopTask = async (taskId) => {
    try {
      await apiService.post(`/api/messaging/bulk/stop/${taskId}`);
      message.success('Task stopped successfully');
      setActiveTask(null);
      setCurrentStep(0);
    } catch (error) {
      message.error('Failed to stop task: ' + error.message);
    }
  };

  const handleViewResults = async (taskId) => {
    try {
      const results = await apiService.get(`/api/messaging/bulk/results/${taskId}`);
      setSelectedTaskResults(results);
      setPreviewModalVisible(true);
    } catch (error) {
      message.error('Failed to load results: ' + error.message);
    }
  };

  const getStatusTag = (status) => {
    const statusConfig = {
      pending: { color: 'default', text: 'Pending' },
      running: { color: 'processing', text: 'Running' },
      completed: { color: 'success', text: 'Completed' },
      failed: { color: 'error', text: 'Failed' },
      cancelled: { color: 'default', text: 'Cancelled' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const steps = [
    {
      title: 'Upload Excel',
      description: 'Upload recipient list',
      icon: <FileExcelOutlined />
    },
    {
      title: 'Configure',
      description: 'Set message and options',
      icon: <MessageOutlined />
    },
    {
      title: 'Send Messages',
      description: 'Monitor progress',
      icon: <SendOutlined />
    }
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <h1>Bulk Messaging (Profile-based)</h1>
          <p>Send messages to users via their profile pages with human-like behavior</p>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              console.log('🔄 [BulkMessaging] Manual refresh triggered');
              setIsInitialDataLoaded(false);
              setIsPolling(false);
              loadInitialData();
            }}
            loading={loading}
          >
            Refresh
          </Button>
        </Col>
      </Row>

      {/* Progress Steps */}
      <Card style={{ marginBottom: 24 }}>
        <Steps current={currentStep} items={steps} />
      </Card>

      <Row gutter={[16, 16]}>
        {/* Step 1: File Upload */}
        <Col xs={24} lg={12}>
          <Card title="Step 1: Upload Recipient List">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="Excel File Format"
                description="Your Excel file should have 2 columns: 'full_name' and 'profile_url'. Profile URLs should be Facebook profile URLs or paths like '/groups/123/user/456'."
                type="info"
                showIcon
              />

              <Upload
                beforeUpload={handleFileUpload}
                accept=".csv,.xlsx,.xls"
                showUploadList={false}
                disabled={loading}
              >
                <Button 
                  icon={<UploadOutlined />} 
                  loading={loading}
                  size="large"
                  block
                >
                  Upload Excel/CSV File
                </Button>
              </Upload>

              {fileValidation && (
                <div>
                  {fileValidation.valid ? (
                    <Alert
                      message="File Validated Successfully"
                      description={
                        <div>
                          <p><strong>Total Recipients:</strong> {fileValidation.total_recipients}</p>
                          {fileValidation.url_type_breakdown && (
                            <div>
                              <p><strong>URL Types:</strong></p>
                              <ul>
                                <li>Groups: {fileValidation.url_type_breakdown.groups}</li>
                                <li>Profile PHP: {fileValidation.url_type_breakdown.profile_php}</li>
                                <li>People: {fileValidation.url_type_breakdown.people}</li>
                                <li>Direct Username: {fileValidation.url_type_breakdown.direct_username}</li>
                                <li>Other: {fileValidation.url_type_breakdown.other}</li>
                              </ul>
                            </div>
                          )}
                        </div>
                      }
                      type="success"
                      showIcon
                    />
                  ) : (
                    <Alert
                      message="File Validation Failed"
                      description={fileValidation.error}
                      type="error"
                      showIcon
                    />
                  )}

                  {fileValidation.preview && fileValidation.preview.length > 0 && (
                    <div style={{ marginTop: 16 }}>
                      <h4>Preview (First 5 rows):</h4>
                      <Table
                        size="small"
                        dataSource={fileValidation.preview}
                        columns={[
                          { title: 'Row', dataIndex: 'row', key: 'row' },
                          { 
                            title: 'Data', 
                            dataIndex: 'data', 
                            key: 'data',
                            render: (data) => (
                              <pre style={{ fontSize: '12px', margin: 0 }}>
                                {JSON.stringify(data, null, 2)}
                              </pre>
                            )
                          }
                        ]}
                        pagination={false}
                      />
                    </div>
                  )}
                </div>
              )}
            </Space>
          </Card>
        </Col>

        {/* Step 2: Configuration */}
        <Col xs={24} lg={12}>
          <Card title="Step 2: Configure Messaging">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleStartBulkMessaging}
            >
              <Form.Item
                name="name"
                label="Task Name"
                rules={[{ required: true, message: 'Please enter task name' }]}
              >
                <Input placeholder="Enter task name" />
              </Form.Item>

              <Form.Item
                name="sender_profile_id"
                label="Sender Profile"
                rules={[{ required: true, message: 'Please select sender profile' }]}
              >
                <Select placeholder="Select profile to send messages from">
                  {profiles.map(profile => (
                    <Option key={profile.id} value={profile.id}>
                      {profile.name} ({profile.facebook_username})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="message_content"
                label="Message Content"
                rules={[{ required: true, message: 'Please enter message content' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="Enter your message content..."
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="delay_between_messages_min"
                    label="Min Delay (seconds)"
                    initialValue={10}
                  >
                    <InputNumber min={5} max={300} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="delay_between_messages_max"
                    label="Max Delay (seconds)"
                    initialValue={30}
                  >
                    <InputNumber min={10} max={600} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="stop_on_consecutive_failures"
                label="Stop after consecutive failures"
                initialValue={3}
              >
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="skip_sent_recipients"
                label="Skip already sent recipients"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Button
                type="primary"
                htmlType="submit"
                icon={<PlayCircleOutlined />}
                loading={loading}
                disabled={!uploadedFile || !fileValidation?.valid}
                block
                size="large"
              >
                Start Bulk Messaging
              </Button>
            </Form>
          </Card>
        </Col>
      </Row>

      {/* Active Task Status */}
      {activeTask && taskProgress[activeTask] && (
        <Card title="Step 3: Messaging Progress" style={{ marginTop: 24 }}>
          <Row gutter={16}>
            <Col xs={24} lg={16}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <strong>Status:</strong> {getStatusTag(taskProgress[activeTask].status)}
                </div>

                <Progress
                  percent={taskProgress[activeTask].progress}
                  status={taskProgress[activeTask].status === 'failed' ? 'exception' :
                          taskProgress[activeTask].status === 'completed' ? 'success' : 'active'}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />

                <Row gutter={16}>
                  <Col span={6}>
                    <Statistic
                      title="Total"
                      value={taskProgress[activeTask].total_recipients}
                      prefix={<UserOutlined />}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Sent"
                      value={taskProgress[activeTask].messages_sent}
                      prefix={<CheckCircleOutlined />}
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Failed"
                      value={taskProgress[activeTask].messages_failed}
                      prefix={<CloseCircleOutlined />}
                      valueStyle={{ color: '#cf1322' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Consecutive Failures"
                      value={taskProgress[activeTask].consecutive_failures}
                      prefix={<ClockCircleOutlined />}
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Col>
                </Row>

                {/* Final Statistics - Show when task is completed */}
                {taskProgress[activeTask].status === 'completed' && taskProgress[activeTask].final_statistics && (
                  <div style={{ marginTop: 24 }}>
                    <Divider orientation="left">📊 Final Statistics</Divider>
                    <Alert
                      message="Task Completed Successfully!"
                      description={
                        <div>
                          <p><strong>🎉 Bulk messaging task has been completed and browser has been closed.</strong></p>
                          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
                            <Col span={8}>
                              <Statistic
                                title="Success Rate"
                                value={taskProgress[activeTask].final_statistics.success_rate}
                                suffix="%"
                                valueStyle={{ color: '#3f8600' }}
                              />
                            </Col>
                            <Col span={8}>
                              <Statistic
                                title="Total Time"
                                value={taskProgress[activeTask].final_statistics.total_time_formatted}
                                valueStyle={{ color: '#1890ff' }}
                              />
                            </Col>
                            <Col span={8}>
                              <Statistic
                                title="Avg Time/Message"
                                value={taskProgress[activeTask].final_statistics.average_time_per_message}
                                suffix="s"
                                valueStyle={{ color: '#722ed1' }}
                              />
                            </Col>
                          </Row>
                          <div style={{ marginTop: 16 }}>
                            <Text type="secondary">
                              🌐 Browser Status: {taskProgress[activeTask].final_statistics.browser_closed ? 'Closed' : 'Open'}
                            </Text>
                            <br />
                            <Text type="secondary">
                              ⏰ Completed at: {new Date(taskProgress[activeTask].final_statistics.completed_at).toLocaleString()}
                            </Text>
                          </div>
                        </div>
                      }
                      type="success"
                      showIcon
                    />
                  </div>
                )}
              </Space>
            </Col>

            <Col xs={24} lg={8}>
              <Space direction="vertical" style={{ width: '100%' }}>
                {taskProgress[activeTask].status === 'running' ? (
                  <Button
                    danger
                    icon={<StopOutlined />}
                    onClick={() => handleStopTask(activeTask)}
                    block
                  >
                    Stop Task
                  </Button>
                ) : taskProgress[activeTask].status === 'completed' ? (
                  <Button
                    type="default"
                    icon={<DeleteOutlined />}
                    onClick={() => {
                      setTaskProgress(prev => {
                        const newProgress = { ...prev };
                        delete newProgress[activeTask];
                        return newProgress;
                      });
                      setActiveTask(null);
                    }}
                    block
                  >
                    Clear Results
                  </Button>
                ) : null}

                <Button
                  icon={<EyeOutlined />}
                  onClick={() => handleViewResults(activeTask)}
                  block
                >
                  View Results
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>
      )}

      {/* Completed Tasks History */}
      {completedTasks.length > 0 && (
        <Card title="📊 Recent Completed Tasks" style={{ marginTop: 24 }}>
          <div style={{ marginBottom: 16 }}>
            <Button
              type="link"
              onClick={() => setShowCompletedTasks(!showCompletedTasks)}
              icon={showCompletedTasks ? <UpOutlined /> : <DownOutlined />}
            >
              {showCompletedTasks ? 'Hide' : 'Show'} {completedTasks.length} completed task(s)
            </Button>
          </div>

          {showCompletedTasks && (
            <List
              dataSource={completedTasks}
              renderItem={(task) => (
                <List.Item
                  actions={[
                    <Button
                      type="link"
                      onClick={() => {
                        setActiveTask(task.task_id);
                        setShowCompletedTasks(false);
                      }}
                    >
                      View Details
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <div>
                        <Text strong>{task.task_name}</Text>
                        <Tag color="green" style={{ marginLeft: 8 }}>Completed</Tag>
                      </div>
                    }
                    description={
                      <div>
                        <Text type="secondary">
                          📤 {task.messages_sent} sent • ❌ {task.messages_failed} failed •
                          📈 {task.success_rate}% success • ⏱️ {task.total_time_formatted}
                        </Text>
                        <br />
                        <Text type="secondary">
                          Completed: {new Date(task.completed_at).toLocaleString()}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </Card>
      )}

      {/* Results Modal */}
      <Modal
        title="Bulk Messaging Results"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        width={1000}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            Close
          </Button>
        ]}
      >
        {selectedTaskResults && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Statistic title="Total Recipients" value={selectedTaskResults.total_recipients} />
              </Col>
              <Col span={6}>
                <Statistic title="Messages Sent" value={selectedTaskResults.messages_sent} />
              </Col>
              <Col span={6}>
                <Statistic title="Failed" value={selectedTaskResults.messages_failed} />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Success Rate"
                  value={selectedTaskResults.success_rate.toFixed(1)}
                  suffix="%"
                />
              </Col>
            </Row>

            <Divider />

            <Row gutter={16}>
              <Col span={12}>
                <h4>Successful Recipients ({selectedTaskResults.successful_recipients.length})</h4>
                <List
                  size="small"
                  dataSource={selectedTaskResults.successful_recipients.slice(0, 10)}
                  renderItem={(item) => (
                    <List.Item>
                      <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                      {item.recipient.full_name}
                    </List.Item>
                  )}
                />
              </Col>
              <Col span={12}>
                <h4>Failed Recipients ({selectedTaskResults.failed_recipients.length})</h4>
                <List
                  size="small"
                  dataSource={selectedTaskResults.failed_recipients.slice(0, 10)}
                  renderItem={(item) => (
                    <List.Item>
                      <CloseCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                      {item.recipient.full_name}
                      <br />
                      <small style={{ color: '#999' }}>{item.result.error}</small>
                    </List.Item>
                  )}
                />
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default BulkMessaging;
